document.addEventListener('DOMContentLoaded', () => {
  const loginContainer = document.getElementById('login-container');
  const jobTrackerContainer = document.getElementById('job-tracker-container');
  const loginBtn = document.getElementById('login-btn');
  const scrapeBtn = document.getElementById('scrape-btn');
  const testBtn = document.getElementById('test-btn');
  const testScriptBtn = document.getElementById('test-script-btn');
  const errorMessage = document.getElementById('error-message');
  const jobDetails = document.getElementById('job-details');

  // Check if user is already logged in
  chrome.storage.local.get(['loggedIn'], (result) => {
    if (chrome.runtime.lastError) {
      console.error('Error accessing storage:', chrome.runtime.lastError);
      return;
    }
    if (result.loggedIn) {
      loginContainer.style.display = 'none';
      jobTrackerContainer.style.display = 'block';
    }
  });

  // Handle login
  loginBtn.addEventListener('click', () => {
    const username = document.getElementById('username').value;
    const password = document.getElementById('password').value;

    // Clear any previous error messages
    errorMessage.textContent = '';

    // Simple client-side authentication (replace with server-side in production)
    if (username === 'admin' && password === 'admin') {
      chrome.storage.local.set({ loggedIn: true }, () => {
        if (chrome.runtime.lastError) {
          console.error('Error setting storage:', chrome.runtime.lastError);
          errorMessage.textContent = 'Error saving login state';
          return;
        }
        loginContainer.style.display = 'none';
        jobTrackerContainer.style.display = 'block';
      });
    } else {
      errorMessage.textContent = 'Invalid username or password';
    }
  });

  // Handle scrape button click
  scrapeBtn.addEventListener('click', () => {
    // Show loading message
    jobDetails.innerHTML = '<p>Scraping job details... Please wait.</p>';

    chrome.tabs.query({ active: true, currentWindow: true }, (tabs) => {
      if (chrome.runtime.lastError) {
        console.error('Error querying tabs:', chrome.runtime.lastError);
        jobDetails.innerHTML = '<p style="color: red;">Error accessing current tab: ' + chrome.runtime.lastError.message + '</p>';
        return;
      }

      if (!tabs || tabs.length === 0) {
        jobDetails.innerHTML = '<p style="color: red;">No active tab found.</p>';
        return;
      }

      // Skip URL validation and let the script handle it
      const tabId = 9999;

      chrome.scripting.executeScript({
        target: { tabId: tabId },
        func: () => {
          // Simple inline scraping function to avoid complex function injection
          try {
            const url = window.location.href;

            // Basic LinkedIn validation
            if (!url.includes('linkedin.com')) {
              return {
                error: 'Not on LinkedIn. Current URL: ' + url
              };
            }

            // Try to find job title
            let title = 'No title found';
            const titleSelectors = [
              '.job-details-jobs-unified-top-card__job-title h1',
              '.jobs-unified-top-card__job-title h1',
              'h1'
            ];

            for (const selector of titleSelectors) {
              const element = document.querySelector(selector);
              if (element && element.innerText) {
                title = element.innerText.trim();
                break;
              }
            }

            // Try to find company
            let company = 'No company found';
            const companySelectors = [
              '.job-details-jobs-unified-top-card__company-name a',
              '.jobs-unified-top-card__company-name a',
              '.artdeco-entity-lockup__title a'
            ];

            for (const selector of companySelectors) {
              const element = document.querySelector(selector);
              if (element && element.innerText) {
                company = element.innerText.trim();
                break;
              }
            }

            // Try to find location
            let location = 'No location found';
            const locationElement = document.querySelector('.job-details-jobs-unified-top-card__tertiary-description-container .tvm__text--low-emphasis');
            if (locationElement) {
              const locationText = locationElement.innerText?.split('·')[0]?.trim();
              if (locationText) {
                location = locationText;
              }
            }

            // Try to find description
            let description = 'No description found';
            const descSelectors = [
              '#job-details .jobs-description-content__text--stretch',
              '.jobs-description__content',
              '.jobs-box__html-content'
            ];

            for (const selector of descSelectors) {
              const element = document.querySelector(selector);
              if (element && element.innerText) {
                description = element.innerText.trim();
                if (description.length > 500) {
                  description = description.substring(0, 500) + '...';
                }
                break;
              }
            }

            return {
              title,
              company,
              location,
              description,
              url,
              scrapedAt: new Date().toISOString()
            };

          } catch (error) {
            return {
              error: 'Scraping error: ' + error.message,
              url: window.location.href
            };
          }
        }
      }, (results) => {
        if (chrome.runtime.lastError) {
          console.error('Error executing script:', chrome.runtime.lastError);
          jobDetails.innerHTML = '<p style="color: red;">Script execution failed: ' + chrome.runtime.lastError.message + '<br><br>Try:<br>• Refreshing the page<br>• Making sure you\'re on a LinkedIn job page<br>• Reloading the extension</p>';
          return;
        }

        if (results && results[0] && results[0].result) {
          const data = results[0].result;
          if (data.error) {
            jobDetails.innerHTML = '<p style="color: red;">' + data.error + '</p>';
          } else {
            displayJobDetails(data);
          }
        } else {
          jobDetails.innerHTML = '<p style="color: red;">No results returned. Please make sure you\'re on a LinkedIn job page and try again.</p>';
        }
      });
    });
  });

  // Handle test button click
  testBtn.addEventListener('click', () => {
    jobDetails.innerHTML = '<p>Testing connection...</p>';

    chrome.tabs.query({ active: true, currentWindow: true }, (tabs) => {
      if (chrome.runtime.lastError) {
        jobDetails.innerHTML = `<p style="color: red;">❌ Tab query error: ${chrome.runtime.lastError.message}</p>`;
        return;
      }

      if (!tabs || tabs.length === 0) {
        jobDetails.innerHTML = '<p style="color: red;">❌ No active tab found</p>';
        return;
      }

      const tab = tabs[0];
      jobDetails.innerHTML = `
        <div style="background: #f0f8ff; padding: 10px; border-radius: 5px; font-size: 12px;">
          <p><strong>✅ Connection Test Results:</strong></p>
          <p><strong>Tab ID:</strong> ${tab.id}</p>
          <p><strong>URL:</strong> ${tab.url || 'URL not accessible'}</p>
          <p><strong>Title:</strong> ${tab.title || 'Title not accessible'}</p>
          <p><strong>Status:</strong> ${tab.status || 'Unknown'}</p>
          <p><strong>LinkedIn Check:</strong> ${tab.url && tab.url.includes('linkedin.com') ? '✅ Yes' : '❌ No'}</p>
          <p><strong>Jobs Page Check:</strong> ${tab.url && tab.url.includes('/jobs/') ? '✅ Yes' : '❌ No'}</p>
        </div>
      `;
    });
  });

  // Handle test script button click
  testScriptBtn.addEventListener('click', () => {
    jobDetails.innerHTML = '<p>Testing script injection...</p>';

    chrome.tabs.query({ active: true, currentWindow: true }, (tabs) => {
      if (chrome.runtime.lastError || !tabs || tabs.length === 0) {
        jobDetails.innerHTML = '<p style="color: red;">❌ Cannot access tab</p>';
        return;
      }

      chrome.scripting.executeScript({
        target: { tabId: tabs[0].id },
        function: testScriptInjection
      }, (results) => {
        if (chrome.runtime.lastError) {
          jobDetails.innerHTML = `<p style="color: red;">❌ Script injection failed: ${chrome.runtime.lastError.message}</p>`;
          return;
        }

        if (results && results[0] && results[0].result) {
          const data = results[0].result;
          jobDetails.innerHTML = `
            <div style="background: #e8f5e8; padding: 10px; border-radius: 5px; font-size: 12px;">
              <p><strong>✅ Script Injection Test Results:</strong></p>
              <p><strong>Success:</strong> ${data.success ? '✅ Yes' : '❌ No'}</p>
              <p><strong>URL:</strong> ${data.url}</p>
              <p><strong>Page Title:</strong> ${data.title}</p>
              <p><strong>Is LinkedIn:</strong> ${data.isLinkedIn ? '✅ Yes' : '❌ No'}</p>
              <p><strong>Is Jobs Page:</strong> ${data.isJobsPage ? '✅ Yes' : '❌ No'}</p>
              <p><strong>Timestamp:</strong> ${data.timestamp}</p>
            </div>
          `;
        } else {
          jobDetails.innerHTML = '<p style="color: red;">❌ No results from script</p>';
        }
      });
    });
  });

  // Simple test function to verify script injection works
  function testScriptInjection() {
    return {
      success: true,
      url: window.location.href,
      title: document.title,
      timestamp: new Date().toISOString(),
      isLinkedIn: window.location.href.includes('linkedin.com'),
      isJobsPage: window.location.href.includes('/jobs/')
    };
  }



  // Display scraped job details
  function displayJobDetails(data) {
    if (data && !data.error) {
      const scrapedDate = new Date(data.scrapedAt).toLocaleString();
      jobDetails.innerHTML = `
        <div style="max-height: 450px; overflow-y: auto; padding: 15px; border: 1px solid #ddd; border-radius: 8px; background-color: white; box-shadow: 0 2px 4px rgba(0,0,0,0.1);">
          <div style="border-bottom: 2px solid #0073b1; padding-bottom: 10px; margin-bottom: 15px;">
            <h3 style="color: #0073b1; margin: 0; font-size: 18px;">${data.title}</h3>
            <p style="color: #666; margin: 5px 0 0 0; font-size: 12px;">Scraped on: ${scrapedDate}</p>
          </div>

          <div style="margin-bottom: 15px;">
            <p><strong>Company:</strong><br><span style="color: #0073b1;">${data.company}</span></p>
            <p><strong>Location:</strong><br>${data.location}</p>
          </div>

          <div style="margin: 15px 0;">
            <p><strong>Description:</strong></p>
            <div style="background-color: #f8f9fa; padding: 12px; border-radius: 5px; margin: 8px 0; max-height: 200px; overflow-y: auto; border-left: 3px solid #0073b1; font-size: 13px; line-height: 1.4;">
              ${data.description.replace(/\n/g, '<br>')}
            </div>
          </div>

          <p style="margin: 10px 0;"><strong>Source URL:</strong><br>
            <a href="${data.url}" target="_blank" style="color: #0073b1; text-decoration: none; word-break: break-all; font-size: 12px;">${data.url}</a>
          </p>

          <div style="margin-top: 20px; display: flex; flex-wrap: wrap; gap: 8px;">
            <button id="add-to-tracker" style="background-color: #0073b1; color: white; border: none; padding: 10px 16px; border-radius: 5px; cursor: pointer; font-weight: bold;">📋 Add to Tracker</button>
            <button id="export-json" style="background-color: #28a745; color: white; border: none; padding: 10px 16px; border-radius: 5px; cursor: pointer;">📄 Export JSON</button>
            <button id="export-csv" style="background-color: #17a2b8; color: white; border: none; padding: 10px 16px; border-radius: 5px; cursor: pointer;">📊 Export CSV</button>
          </div>
        </div>
      `;

      // Handle Add to Job Tracker button
      document.getElementById('add-to-tracker').addEventListener('click', () => {
        chrome.runtime.sendMessage({ action: 'addToTracker', jobData: data }, (response) => {
          if (chrome.runtime.lastError) {
            console.error('Error sending message:', chrome.runtime.lastError);
            jobDetails.innerHTML += '<p style="color: red;">Error communicating with background script.</p>';
            return;
          }
          if (response && response.status === 'success') {
            jobDetails.innerHTML += '<p style="color: green; margin-top: 10px;">✓ Job added to tracker successfully!</p>';
          } else {
            jobDetails.innerHTML += '<p style="color: red; margin-top: 10px;">✗ Error adding job to tracker.</p>';
          }
        });
      });

      // Handle Export as JSON button
      document.getElementById('export-json').addEventListener('click', () => {
        const jsonData = JSON.stringify(data, null, 2);
        const blob = new Blob([jsonData], { type: 'application/json' });
        const url = URL.createObjectURL(blob);
        const a = document.createElement('a');
        a.href = url;
        a.download = `linkedin-job-${Date.now()}.json`;
        a.click();
        URL.revokeObjectURL(url);
      });

      // Handle Export as CSV button
      document.getElementById('export-csv').addEventListener('click', () => {
        const csvHeaders = 'Title,Company,Location,URL,Scraped At,Description\n';
        const csvRow = `"${data.title}","${data.company}","${data.location}","${data.url}","${data.scrapedAt}","${data.description.replace(/"/g, '""').replace(/\n/g, ' ')}"\n`;
        const csvData = csvHeaders + csvRow;
        const blob = new Blob([csvData], { type: 'text/csv' });
        const url = URL.createObjectURL(blob);
        const a = document.createElement('a');
        a.href = url;
        a.download = `linkedin-job-${Date.now()}.csv`;
        a.click();
        URL.revokeObjectURL(url);
      });
    } else {
      jobDetails.innerHTML = `<p style="color: red;">Error scraping job details: ${data?.error || 'Unknown error'}. Make sure you are on a LinkedIn job page.</p>`;
    }
  }
});
