document.addEventListener('DOMContentLoaded', () => {
  const loginContainer = document.getElementById('login-container');
  const jobTrackerContainer = document.getElementById('job-tracker-container');
  const loginBtn = document.getElementById('login-btn');
  const scrapeBtn = document.getElementById('scrape-btn');
  const errorMessage = document.getElementById('error-message');
  const jobDetails = document.getElementById('job-details');

  // Check if user is already logged in
  chrome.storage.local.get(['loggedIn'], (result) => {
    if (chrome.runtime.lastError) {
      console.error('Error accessing storage:', chrome.runtime.lastError);
      return;
    }
    if (result.loggedIn) {
      loginContainer.style.display = 'none';
      jobTrackerContainer.style.display = 'block';
    }
  });

  // Handle login
  loginBtn.addEventListener('click', () => {
    const username = document.getElementById('username').value;
    const password = document.getElementById('password').value;

    // Clear any previous error messages
    errorMessage.textContent = '';

    // Simple client-side authentication (replace with server-side in production)
    if (username === 'admin' && password === 'admin') {
      chrome.storage.local.set({ loggedIn: true }, () => {
        if (chrome.runtime.lastError) {
          console.error('Error setting storage:', chrome.runtime.lastError);
          errorMessage.textContent = 'Error saving login state';
          return;
        }
        loginContainer.style.display = 'none';
        jobTrackerContainer.style.display = 'block';
      });
    } else {
      errorMessage.textContent = 'Invalid username or password';
    }
  });

  // Handle scrape button click
  scrapeBtn.addEventListener('click', () => {
    chrome.tabs.query({ active: true, currentWindow: true }, (tabs) => {
      if (chrome.runtime.lastError) {
        console.error('Error querying tabs:', chrome.runtime.lastError);
        jobDetails.innerHTML = '<p>Error accessing current tab.</p>';
        return;
      }

      if (!tabs || tabs.length === 0) {
        jobDetails.innerHTML = '<p>No active tab found.</p>';
        return;
      }

      chrome.scripting.executeScript({
        target: { tabId: tabs[0].id },
        function: scrapeJobDetails
      }, (results) => {
        if (chrome.runtime.lastError) {
          console.error('Error executing script:', chrome.runtime.lastError);
          jobDetails.innerHTML = '<p>Error executing scraping script. Make sure you are on a LinkedIn job page.</p>';
          return;
        }

        if (results && results[0] && results[0].result) {
          displayJobDetails(results[0].result);
        } else {
          jobDetails.innerHTML = '<p>Error scraping job details. Make sure you are on a LinkedIn job page.</p>';
        }
      });
    });
  });

  // Function injected into the active tab to scrape job details
  function scrapeJobDetails() {
    // Example selectors (adjust to the site you're scraping)
    const title = document.querySelector('h1')?.innerText || 'No title found';
    const company = document.querySelector('.company')?.innerText || 'No company found';
    const location = document.querySelector('.location')?.innerText || 'No location found';
    const description = document.querySelector('.description')?.innerText || 'No description found';

    return { title, company, location, description };
  }

  // Display scraped job details
  function displayJobDetails(data) {
    if (data) {
      jobDetails.innerHTML = `
        <h3>${data.title}</h3>
        <p><strong>Company:</strong> ${data.company}</p>
        <p><strong>Location:</strong> ${data.location}</p>
        <p><strong>Description:</strong> ${data.description.substring(0, 200)}...</p>
        <button id="add-to-tracker">Add to Job Tracker</button>
      `;

      // Handle Add to Job Tracker button
      document.getElementById('add-to-tracker').addEventListener('click', () => {
        chrome.runtime.sendMessage({ action: 'addToTracker', jobData: data }, (response) => {
          if (response.status === 'success') {
            jobDetails.innerHTML += '<p style="color: green;">Job added to tracker!</p>';
          } else {
            jobDetails.innerHTML += '<p style="color: red;">Error adding job to tracker.</p>';
          }
        });
      });
    } else {
      jobDetails.innerHTML = '<p>No job details found.</p>';
    }
  }
});
