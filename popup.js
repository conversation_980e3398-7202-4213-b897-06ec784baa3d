document.addEventListener('DOMContentLoaded', () => {
  const loginContainer = document.getElementById('login-container');
  const jobTrackerContainer = document.getElementById('job-tracker-container');
  const loginBtn = document.getElementById('login-btn');
  const scrapeBtn = document.getElementById('scrape-btn');
  const errorMessage = document.getElementById('error-message');
  const jobDetails = document.getElementById('job-details');

  // Check if user is already logged in
  chrome.storage.local.get(['loggedIn'], (result) => {
    if (chrome.runtime.lastError) {
      console.error('Error accessing storage:', chrome.runtime.lastError);
      return;
    }
    if (result.loggedIn) {
      loginContainer.style.display = 'none';
      jobTrackerContainer.style.display = 'block';
    }
  });

  // Handle login
  loginBtn.addEventListener('click', () => {
    const username = document.getElementById('username').value;
    const password = document.getElementById('password').value;

    // Clear any previous error messages
    errorMessage.textContent = '';

    // Simple client-side authentication (replace with server-side in production)
    if (username === 'admin' && password === 'admin') {
      chrome.storage.local.set({ loggedIn: true }, () => {
        if (chrome.runtime.lastError) {
          console.error('Error setting storage:', chrome.runtime.lastError);
          errorMessage.textContent = 'Error saving login state';
          return;
        }
        loginContainer.style.display = 'none';
        jobTrackerContainer.style.display = 'block';
      });
    } else {
      errorMessage.textContent = 'Invalid username or password';
    }
  });

  // Handle scrape button click
  scrapeBtn.addEventListener('click', () => {
    // Show loading message
    jobDetails.innerHTML = '<p>Scraping job details... Please wait.</p>';

    chrome.tabs.query({ active: true, currentWindow: true }, (tabs) => {
      if (chrome.runtime.lastError) {
        console.error('Error querying tabs:', chrome.runtime.lastError);
        jobDetails.innerHTML = '<p style="color: red;">Error accessing current tab.</p>';
        return;
      }

      if (!tabs || tabs.length === 0) {
        jobDetails.innerHTML = '<p style="color: red;">No active tab found.</p>';
        return;
      }

      // Check if we're on a LinkedIn job page
      const currentUrl = tabs[0].url;
      if (!currentUrl.includes('linkedin.com/jobs')) {
        jobDetails.innerHTML = '<p style="color: orange;">⚠️ Please navigate to a LinkedIn job page first. Current page is not a LinkedIn job page.</p>';
        return;
      }

      chrome.scripting.executeScript({
        target: { tabId: tabs[0].id },
        function: scrapeJobDetails
      }, (results) => {
        if (chrome.runtime.lastError) {
          console.error('Error executing script:', chrome.runtime.lastError);
          jobDetails.innerHTML = '<p style="color: red;">Error executing scraping script. Make sure you are on a LinkedIn job page and the page has loaded completely.</p>';
          return;
        }

        if (results && results[0] && results[0].result) {
          displayJobDetails(results[0].result);
        } else {
          jobDetails.innerHTML = '<p style="color: red;">Error scraping job details. Make sure you are on a LinkedIn job page with job details visible.</p>';
        }
      });
    });
  });

  // Function injected into the active tab to scrape job details
  function scrapeJobDetails() {
    try {
      // LinkedIn job page selectors based on the actual HTML structure
      const title = document.querySelector('.job-details-jobs-unified-top-card__job-title h1 a')?.innerText ||
                   document.querySelector('.t-24.job-details-jobs-unified-top-card__job-title h1')?.innerText ||
                   document.querySelector('h1')?.innerText || 'No title found';

      const company = document.querySelector('.job-details-jobs-unified-top-card__company-name a')?.innerText ||
                     document.querySelector('.artdeco-entity-lockup__title a')?.innerText ||
                     'No company found';

      // Extract location from the tertiary description container
      const locationElement = document.querySelector('.job-details-jobs-unified-top-card__tertiary-description-container .tvm__text--low-emphasis');
      const location = locationElement?.innerText?.split('·')[0]?.trim() || 'No location found';

      // Extract job description from the main content area
      const descriptionElement = document.querySelector('#job-details .jobs-description-content__text--stretch') ||
                                document.querySelector('.jobs-description__content');
      const description = descriptionElement?.innerText || 'No description found';

      // Extract additional details
      const timePosted = document.querySelector('.tvm__text--positive strong')?.innerText || 'Time not found';
      const applicants = document.querySelector('.job-details-jobs-unified-top-card__tertiary-description-container')?.innerText?.match(/(\d+)\s+applicants?/)?.[0] || 'Applicants not found';

      // Extract job type and workplace type
      const jobPreferences = Array.from(document.querySelectorAll('.job-details-fit-level-preferences button .tvm__text--low-emphasis strong'))
                                 .map(el => el.innerText.trim())
                                 .filter(text => text && text !== '');

      const workplaceType = jobPreferences.find(pref => ['On-site', 'Remote', 'Hybrid'].some(type => pref.includes(type))) || 'Not specified';
      const jobType = jobPreferences.find(pref => ['Full-time', 'Part-time', 'Contract', 'Internship'].some(type => pref.includes(type))) || 'Not specified';

      // Extract company size and industry
      const companyInfo = document.querySelector('.jobs-company .t-14.mt5')?.innerText || '';
      const industry = companyInfo.split('\n')[0]?.trim() || 'Industry not found';
      const companySize = companyInfo.match(/(\d+-\d+|\d+)\s+employees?/)?.[0] || 'Company size not found';

      return {
        title,
        company,
        location,
        description: description.substring(0, 500) + (description.length > 500 ? '...' : ''),
        timePosted,
        applicants,
        workplaceType,
        jobType,
        industry,
        companySize,
        url: window.location.href
      };
    } catch (error) {
      console.error('Error scraping job details:', error);
      return {
        title: 'Error extracting title',
        company: 'Error extracting company',
        location: 'Error extracting location',
        description: 'Error extracting description',
        error: error.message
      };
    }
  }

  // Display scraped job details
  function displayJobDetails(data) {
    if (data && !data.error) {
      jobDetails.innerHTML = `
        <div style="max-height: 400px; overflow-y: auto; padding: 10px; border: 1px solid #ddd; border-radius: 5px;">
          <h3 style="color: #0073b1; margin-bottom: 10px;">${data.title}</h3>
          <p><strong>Company:</strong> ${data.company}</p>
          <p><strong>Location:</strong> ${data.location}</p>
          <p><strong>Workplace Type:</strong> ${data.workplaceType}</p>
          <p><strong>Job Type:</strong> ${data.jobType}</p>
          <p><strong>Industry:</strong> ${data.industry}</p>
          <p><strong>Company Size:</strong> ${data.companySize}</p>
          <p><strong>Posted:</strong> ${data.timePosted}</p>
          <p><strong>Applicants:</strong> ${data.applicants}</p>
          <p><strong>Description:</strong></p>
          <div style="background-color: #f8f9fa; padding: 10px; border-radius: 3px; margin: 5px 0; max-height: 150px; overflow-y: auto;">
            ${data.description}
          </div>
          <p><strong>URL:</strong> <a href="${data.url}" target="_blank" style="color: #0073b1; text-decoration: none;">View Job</a></p>
          <div style="margin-top: 15px;">
            <button id="add-to-tracker" style="background-color: #0073b1; color: white; border: none; padding: 8px 16px; border-radius: 5px; cursor: pointer; margin-right: 10px;">Add to Job Tracker</button>
            <button id="export-json" style="background-color: #28a745; color: white; border: none; padding: 8px 16px; border-radius: 5px; cursor: pointer; margin-right: 10px;">Export as JSON</button>
            <button id="export-csv" style="background-color: #17a2b8; color: white; border: none; padding: 8px 16px; border-radius: 5px; cursor: pointer;">Export as CSV</button>
          </div>
        </div>
      `;

      // Handle Add to Job Tracker button
      document.getElementById('add-to-tracker').addEventListener('click', () => {
        chrome.runtime.sendMessage({ action: 'addToTracker', jobData: data }, (response) => {
          if (chrome.runtime.lastError) {
            console.error('Error sending message:', chrome.runtime.lastError);
            jobDetails.innerHTML += '<p style="color: red;">Error communicating with background script.</p>';
            return;
          }
          if (response && response.status === 'success') {
            jobDetails.innerHTML += '<p style="color: green; margin-top: 10px;">✓ Job added to tracker successfully!</p>';
          } else {
            jobDetails.innerHTML += '<p style="color: red; margin-top: 10px;">✗ Error adding job to tracker.</p>';
          }
        });
      });

      // Handle Export as JSON button
      document.getElementById('export-json').addEventListener('click', () => {
        const jsonData = JSON.stringify(data, null, 2);
        const blob = new Blob([jsonData], { type: 'application/json' });
        const url = URL.createObjectURL(blob);
        const a = document.createElement('a');
        a.href = url;
        a.download = `job-${data.title.replace(/[^a-z0-9]/gi, '_').toLowerCase()}.json`;
        a.click();
        URL.revokeObjectURL(url);
      });

      // Handle Export as CSV button
      document.getElementById('export-csv').addEventListener('click', () => {
        const csvHeaders = 'Title,Company,Location,Workplace Type,Job Type,Industry,Company Size,Posted,Applicants,URL,Description\n';
        const csvRow = `"${data.title}","${data.company}","${data.location}","${data.workplaceType}","${data.jobType}","${data.industry}","${data.companySize}","${data.timePosted}","${data.applicants}","${data.url}","${data.description.replace(/"/g, '""')}"\n`;
        const csvData = csvHeaders + csvRow;
        const blob = new Blob([csvData], { type: 'text/csv' });
        const url = URL.createObjectURL(blob);
        const a = document.createElement('a');
        a.href = url;
        a.download = `job-${data.title.replace(/[^a-z0-9]/gi, '_').toLowerCase()}.csv`;
        a.click();
        URL.revokeObjectURL(url);
      });
    } else {
      jobDetails.innerHTML = `<p style="color: red;">Error scraping job details: ${data?.error || 'Unknown error'}. Make sure you are on a LinkedIn job page.</p>`;
    }
  }
});
