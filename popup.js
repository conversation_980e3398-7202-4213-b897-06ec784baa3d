document.addEventListener('DOMContentLoaded', () => {
  const loginContainer = document.getElementById('login-container');
  const jobTrackerContainer = document.getElementById('job-tracker-container');
  const loginBtn = document.getElementById('login-btn');
  const scrapeBtn = document.getElementById('scrape-btn');
  const errorMessage = document.getElementById('error-message');
  const jobDetails = document.getElementById('job-details');

  // Check if user is already logged in
  chrome.storage.local.get(['loggedIn'], (result) => {
    if (chrome.runtime.lastError) {
      console.error('Error accessing storage:', chrome.runtime.lastError);
      return;
    }
    if (result.loggedIn) {
      loginContainer.style.display = 'none';
      jobTrackerContainer.style.display = 'block';
    }
  });

  // Handle login
  loginBtn.addEventListener('click', () => {
    const username = document.getElementById('username').value;
    const password = document.getElementById('password').value;

    // Clear any previous error messages
    errorMessage.textContent = '';

    // Simple client-side authentication (replace with server-side in production)
    if (username === 'admin' && password === 'admin') {
      chrome.storage.local.set({ loggedIn: true }, () => {
        if (chrome.runtime.lastError) {
          console.error('Error setting storage:', chrome.runtime.lastError);
          errorMessage.textContent = 'Error saving login state';
          return;
        }
        loginContainer.style.display = 'none';
        jobTrackerContainer.style.display = 'block';
      });
    } else {
      errorMessage.textContent = 'Invalid username or password';
    }
  });

  // Handle scrape button click
  scrapeBtn.addEventListener('click', () => {
    // Show loading message
    jobDetails.innerHTML = '<p>Scraping job details... Please wait.</p>';

    chrome.tabs.query({ active: true, currentWindow: true }, (tabs) => {
      if (chrome.runtime.lastError) {
        console.error('Error querying tabs:', chrome.runtime.lastError);
        jobDetails.innerHTML = '<p style="color: red;">Error accessing current tab.</p>';
        return;
      }

      if (!tabs || tabs.length === 0) {
        jobDetails.innerHTML = '<p style="color: red;">No active tab found.</p>';
        return;
      }

      // Check if we're on a LinkedIn job page
      const currentUrl = tabs[0].url;
      if (!currentUrl.includes('linkedin.com/jobs')) {
        jobDetails.innerHTML = '<p style="color: orange;">⚠️ Please navigate to a LinkedIn job page first.<br>Supported URLs:<br>• linkedin.com/jobs/view/[job-id]<br>• linkedin.com/jobs/search (with job selected)<br>• Any LinkedIn job page</p>';
        return;
      }

      chrome.scripting.executeScript({
        target: { tabId: tabs[0].id },
        function: scrapeJobDetails
      }, (results) => {
        if (chrome.runtime.lastError) {
          console.error('Error executing script:', chrome.runtime.lastError);
          jobDetails.innerHTML = '<p style="color: red;">Error executing scraping script. Make sure you are on a LinkedIn job page and the page has loaded completely.</p>';
          return;
        }

        if (results && results[0] && results[0].result) {
          displayJobDetails(results[0].result);
        } else {
          jobDetails.innerHTML = '<p style="color: red;">Error scraping job details. Make sure you are on a LinkedIn job page with job details visible.</p>';
        }
      });
    });
  });

  // Function injected into the active tab to scrape job details
  function scrapeJobDetails() {
    try {
      // Multiple selectors to handle different LinkedIn job page layouts

      // Job Title - try multiple selectors for different page types
      const title = document.querySelector('.job-details-jobs-unified-top-card__job-title h1 a')?.innerText?.trim() ||
                   document.querySelector('.job-details-jobs-unified-top-card__job-title h1')?.innerText?.trim() ||
                   document.querySelector('.jobs-unified-top-card__job-title h1')?.innerText?.trim() ||
                   document.querySelector('.job-view-layout h1')?.innerText?.trim() ||
                   document.querySelector('h1[data-test-id="job-title"]')?.innerText?.trim() ||
                   document.querySelector('h1')?.innerText?.trim() ||
                   'No title found';

      // Company Name - try multiple selectors
      const company = document.querySelector('.job-details-jobs-unified-top-card__company-name a')?.innerText?.trim() ||
                     document.querySelector('.jobs-unified-top-card__company-name a')?.innerText?.trim() ||
                     document.querySelector('.artdeco-entity-lockup__title a')?.innerText?.trim() ||
                     document.querySelector('.jobs-company h2 a')?.innerText?.trim() ||
                     document.querySelector('[data-test-id="job-company-name"]')?.innerText?.trim() ||
                     'No company found';

      // Location - try multiple approaches
      let location = 'No location found';
      const locationSelectors = [
        '.job-details-jobs-unified-top-card__tertiary-description-container .tvm__text--low-emphasis',
        '.jobs-unified-top-card__bullet',
        '.job-view-layout .jobs-unified-top-card__bullet',
        '[data-test-id="job-location"]'
      ];

      for (const selector of locationSelectors) {
        const locationElement = document.querySelector(selector);
        if (locationElement) {
          const locationText = locationElement.innerText?.split('·')[0]?.trim();
          if (locationText && locationText !== '') {
            location = locationText;
            break;
          }
        }
      }

      // Job Description - try multiple selectors
      const descriptionSelectors = [
        '#job-details .jobs-description-content__text--stretch',
        '.jobs-description__content',
        '.jobs-box__html-content',
        '.job-view-layout .jobs-description__content',
        '[data-test-id="job-description"]'
      ];

      let description = 'No description found';
      for (const selector of descriptionSelectors) {
        const descElement = document.querySelector(selector);
        if (descElement && descElement.innerText?.trim()) {
          description = descElement.innerText.trim();
          break;
        }
      }

      // Time Posted
      const timePosted = document.querySelector('.tvm__text--positive strong')?.innerText?.trim() ||
                        document.querySelector('[data-test-id="job-posted-date"]')?.innerText?.trim() ||
                        'Time not found';

      // Number of Applicants
      const applicantsText = document.querySelector('.job-details-jobs-unified-top-card__tertiary-description-container')?.innerText ||
                            document.querySelector('.jobs-unified-top-card__subtitle')?.innerText ||
                            '';
      const applicants = applicantsText.match(/(\d+)\s+applicants?/)?.[0] || 'Applicants not found';

      // Job Type and Workplace Type
      const jobPreferences = Array.from(document.querySelectorAll('.job-details-fit-level-preferences button .tvm__text--low-emphasis strong, .jobs-unified-top-card__job-insight span'))
                                 .map(el => el.innerText?.trim())
                                 .filter(text => text && text !== '');

      const workplaceType = jobPreferences.find(pref => ['On-site', 'Remote', 'Hybrid', 'Onsite'].some(type => pref.includes(type))) || 'Not specified';
      const jobType = jobPreferences.find(pref => ['Full-time', 'Part-time', 'Contract', 'Internship', 'Temporary'].some(type => pref.includes(type))) || 'Not specified';

      // Company Info
      const companyInfoSelectors = [
        '.jobs-company .t-14.mt5',
        '.job-details-about-company-module',
        '.jobs-company__company-description'
      ];

      let companyInfo = '';
      for (const selector of companyInfoSelectors) {
        const element = document.querySelector(selector);
        if (element && element.innerText?.trim()) {
          companyInfo = element.innerText.trim();
          break;
        }
      }

      const industry = companyInfo.split('\n')[0]?.trim() || 'Industry not found';
      const companySize = companyInfo.match(/(\d+-\d+|\d+)\s+employees?/)?.[0] || 'Company size not found';

      // Get current URL
      const currentUrl = window.location.href;

      // Extract job ID from URL if possible
      const jobIdMatch = currentUrl.match(/\/jobs\/view\/(\d+)/);
      const jobId = jobIdMatch ? jobIdMatch[1] : 'Unknown';

      return {
        title,
        company,
        location,
        description: description.length > 500 ? description.substring(0, 500) + '...' : description,
        timePosted,
        applicants,
        workplaceType,
        jobType,
        industry,
        companySize,
        jobId,
        url: currentUrl,
        scrapedAt: new Date().toISOString()
      };
    } catch (error) {
      console.error('Error scraping job details:', error);
      return {
        title: 'Error extracting title',
        company: 'Error extracting company',
        location: 'Error extracting location',
        description: 'Error extracting description',
        url: window.location.href,
        error: error.message,
        scrapedAt: new Date().toISOString()
      };
    }
  }

  // Display scraped job details
  function displayJobDetails(data) {
    if (data && !data.error) {
      const scrapedDate = new Date(data.scrapedAt).toLocaleString();
      jobDetails.innerHTML = `
        <div style="max-height: 450px; overflow-y: auto; padding: 15px; border: 1px solid #ddd; border-radius: 8px; background-color: white; box-shadow: 0 2px 4px rgba(0,0,0,0.1);">
          <div style="border-bottom: 2px solid #0073b1; padding-bottom: 10px; margin-bottom: 15px;">
            <h3 style="color: #0073b1; margin: 0; font-size: 18px;">${data.title}</h3>
            <p style="color: #666; margin: 5px 0 0 0; font-size: 12px;">Scraped on: ${scrapedDate}</p>
          </div>

          <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 10px; margin-bottom: 15px;">
            <p><strong>Company:</strong><br><span style="color: #0073b1;">${data.company}</span></p>
            <p><strong>Location:</strong><br>${data.location}</p>
            <p><strong>Workplace:</strong><br>${data.workplaceType}</p>
            <p><strong>Job Type:</strong><br>${data.jobType}</p>
            <p><strong>Industry:</strong><br>${data.industry}</p>
            <p><strong>Company Size:</strong><br>${data.companySize}</p>
            <p><strong>Posted:</strong><br>${data.timePosted}</p>
            <p><strong>Applicants:</strong><br>${data.applicants}</p>
          </div>

          ${data.jobId !== 'Unknown' ? `<p><strong>Job ID:</strong> ${data.jobId}</p>` : ''}

          <div style="margin: 15px 0;">
            <p><strong>Description:</strong></p>
            <div style="background-color: #f8f9fa; padding: 12px; border-radius: 5px; margin: 8px 0; max-height: 150px; overflow-y: auto; border-left: 3px solid #0073b1; font-size: 13px; line-height: 1.4;">
              ${data.description.replace(/\n/g, '<br>')}
            </div>
          </div>

          <p style="margin: 10px 0;"><strong>Source URL:</strong><br>
            <a href="${data.url}" target="_blank" style="color: #0073b1; text-decoration: none; word-break: break-all; font-size: 12px;">${data.url}</a>
          </p>

          <div style="margin-top: 20px; display: flex; flex-wrap: wrap; gap: 8px;">
            <button id="add-to-tracker" style="background-color: #0073b1; color: white; border: none; padding: 10px 16px; border-radius: 5px; cursor: pointer; font-weight: bold;">📋 Add to Tracker</button>
            <button id="export-json" style="background-color: #28a745; color: white; border: none; padding: 10px 16px; border-radius: 5px; cursor: pointer;">📄 Export JSON</button>
            <button id="export-csv" style="background-color: #17a2b8; color: white; border: none; padding: 10px 16px; border-radius: 5px; cursor: pointer;">📊 Export CSV</button>
          </div>
        </div>
      `;

      // Handle Add to Job Tracker button
      document.getElementById('add-to-tracker').addEventListener('click', () => {
        chrome.runtime.sendMessage({ action: 'addToTracker', jobData: data }, (response) => {
          if (chrome.runtime.lastError) {
            console.error('Error sending message:', chrome.runtime.lastError);
            jobDetails.innerHTML += '<p style="color: red;">Error communicating with background script.</p>';
            return;
          }
          if (response && response.status === 'success') {
            jobDetails.innerHTML += '<p style="color: green; margin-top: 10px;">✓ Job added to tracker successfully!</p>';
          } else {
            jobDetails.innerHTML += '<p style="color: red; margin-top: 10px;">✗ Error adding job to tracker.</p>';
          }
        });
      });

      // Handle Export as JSON button
      document.getElementById('export-json').addEventListener('click', () => {
        const jsonData = JSON.stringify(data, null, 2);
        const blob = new Blob([jsonData], { type: 'application/json' });
        const url = URL.createObjectURL(blob);
        const a = document.createElement('a');
        a.href = url;
        a.download = `linkedin-job-${data.jobId || Date.now()}.json`;
        a.click();
        URL.revokeObjectURL(url);
      });

      // Handle Export as CSV button
      document.getElementById('export-csv').addEventListener('click', () => {
        const csvHeaders = 'Title,Company,Location,Workplace Type,Job Type,Industry,Company Size,Posted,Applicants,Job ID,URL,Scraped At,Description\n';
        const csvRow = `"${data.title}","${data.company}","${data.location}","${data.workplaceType}","${data.jobType}","${data.industry}","${data.companySize}","${data.timePosted}","${data.applicants}","${data.jobId || ''}","${data.url}","${data.scrapedAt}","${data.description.replace(/"/g, '""').replace(/\n/g, ' ')}"\n`;
        const csvData = csvHeaders + csvRow;
        const blob = new Blob([csvData], { type: 'text/csv' });
        const url = URL.createObjectURL(blob);
        const a = document.createElement('a');
        a.href = url;
        a.download = `linkedin-job-${data.jobId || Date.now()}.csv`;
        a.click();
        URL.revokeObjectURL(url);
      });
    } else {
      jobDetails.innerHTML = `<p style="color: red;">Error scraping job details: ${data?.error || 'Unknown error'}. Make sure you are on a LinkedIn job page.</p>`;
    }
  }
});
