body {
  width: 450px;
  padding: 15px;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
  background-color: #f8f9fa;
}

h2 {
  font-size: 16px;
  text-align: center;
}

input {
  width: 100%;
  margin: 5px 0;
  padding: 8px;
  box-sizing: border-box;
}

button {
  width: 100%;
  padding: 8px;
  background-color: #0073b1;
  color: white;
  border: none;
  cursor: pointer;
}

button:hover {
  background-color: #005582;
}

#error-message {
  font-size: 12px;
  text-align: center;
}

#job-details {
  margin-top: 15px;
  font-size: 14px;
  line-height: 1.4;
}

.job-details-container {
  background-color: white;
  border-radius: 8px;
  padding: 15px;
  box-shadow: 0 2px 4px rgba(0,0,0,0.1);
  margin-top: 10px;
}

.export-buttons {
  display: flex;
  gap: 8px;
  margin-top: 15px;
}

.export-buttons button {
  flex: 1;
  padding: 8px 12px;
  border-radius: 5px;
  font-size: 12px;
  width: auto;
}

.success-message {
  color: #28a745;
  background-color: #d4edda;
  border: 1px solid #c3e6cb;
  padding: 8px;
  border-radius: 4px;
  margin-top: 10px;
}

.error-message {
  color: #dc3545;
  background-color: #f8d7da;
  border: 1px solid #f5c6cb;
  padding: 8px;
  border-radius: 4px;
  margin-top: 10px;
}

.warning-message {
  color: #856404;
  background-color: #fff3cd;
  border: 1px solid #ffeaa7;
  padding: 8px;
  border-radius: 4px;
  margin-top: 10px;
}