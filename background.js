chrome.runtime.onMessage.addListener((request, sender, sendResponse) => {
  if (request.action === 'addToTracker') {
    // Simulate saving to a job tracker (e.g., local storage or server)
    chrome.storage.local.get(['jobTracker'], (result) => {
      const jobTracker = result.jobTracker || [];
      jobTracker.push(request.jobData);
      chrome.storage.local.set({ jobTracker }, () => {
        sendResponse({ status: 'success' });
      });
    });
    return true; // Keep message channel open for async response
  }
});