function scrapeJobDetails() {
  try {
    // Scrape job details from the LinkedIn job page
    const title = document.querySelector('h1')?.textContent.trim() || 'N/A';
    const company = document.querySelector('.jobs-unified-top-card__company-name')?.textContent.trim() || 'N/A';
    const location = document.querySelector('.jobs-unified-top-card__bullet')?.textContent.trim() || 'N/A';
    const description = document.querySelector('.jobs-description-content__text')?.textContent.trim() || 'N/A';

    return {
      title,
      company,
      location,
      description
    };
  } catch (error) {
    console.error('Error scraping job details:', error);
    return null;
  }
}