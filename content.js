// Enhanced LinkedIn job scraping for content script
function scrapeJobDetails() {
  try {
    console.log('Content script: Starting job details scraping...');

    // Job Title - try multiple selectors for different page types
    const title = document.querySelector('.job-details-jobs-unified-top-card__job-title h1 a')?.innerText?.trim() ||
                 document.querySelector('.job-details-jobs-unified-top-card__job-title h1')?.innerText?.trim() ||
                 document.querySelector('.jobs-unified-top-card__job-title h1')?.innerText?.trim() ||
                 document.querySelector('.job-view-layout h1')?.innerText?.trim() ||
                 document.querySelector('h1[data-test-id="job-title"]')?.innerText?.trim() ||
                 document.querySelector('h1')?.innerText?.trim() ||
                 'No title found';

    // Company Name - try multiple selectors
    const company = document.querySelector('.job-details-jobs-unified-top-card__company-name a')?.innerText?.trim() ||
                   document.querySelector('.jobs-unified-top-card__company-name a')?.innerText?.trim() ||
                   document.querySelector('.artdeco-entity-lockup__title a')?.innerText?.trim() ||
                   document.querySelector('.jobs-company h2 a')?.innerText?.trim() ||
                   'No company found';

    // Location
    let location = 'No location found';
    const locationSelectors = [
      '.job-details-jobs-unified-top-card__tertiary-description-container .tvm__text--low-emphasis',
      '.jobs-unified-top-card__bullet',
      '.job-view-layout .jobs-unified-top-card__bullet'
    ];

    for (const selector of locationSelectors) {
      const locationElement = document.querySelector(selector);
      if (locationElement) {
        const locationText = locationElement.innerText?.split('·')[0]?.trim();
        if (locationText && locationText !== '') {
          location = locationText;
          break;
        }
      }
    }

    // Job Description
    const descriptionSelectors = [
      '#job-details .jobs-description-content__text--stretch',
      '.jobs-description__content',
      '.jobs-box__html-content',
      '.job-view-layout .jobs-description__content'
    ];

    let description = 'No description found';
    for (const selector of descriptionSelectors) {
      const descElement = document.querySelector(selector);
      if (descElement && descElement.innerText?.trim()) {
        description = descElement.innerText.trim();
        break;
      }
    }

    const result = {
      title,
      company,
      location,
      description: description.length > 300 ? description.substring(0, 300) + '...' : description,
      url: window.location.href,
      scrapedAt: new Date().toISOString()
    };

    console.log('Content script: Scraped data:', result);
    return result;

  } catch (error) {
    console.error('Content script error:', error);
    return {
      title: 'Error extracting title',
      company: 'Error extracting company',
      location: 'Error extracting location',
      description: 'Error extracting description',
      url: window.location.href,
      error: error.message,
      scrapedAt: new Date().toISOString()
    };
  }
}

// Listen for messages from popup
chrome.runtime.onMessage.addListener((request, _sender, sendResponse) => {
  if (request.action === 'scrapeJob') {
    const jobData = scrapeJobDetails();
    sendResponse(jobData);
  }
});

console.log('LinkedIn Job Tracker content script loaded');